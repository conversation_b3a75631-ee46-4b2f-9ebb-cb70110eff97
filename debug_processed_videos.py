#!/usr/bin/env python3
"""
Debug Processed Videos
=====================

Examine the actual content of MediaPipe processed videos to understand
why face detection is failing on the crops.
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import argparse

def analyze_video_content(npy_path: str, output_dir: str):
    """Analyze the content of a processed video"""
    
    print(f"\n🔍 Analyzing: {Path(npy_path).name}")
    
    try:
        # Load video array
        video_array = np.load(npy_path)  # Shape: [T, H, W, C]
        print(f"   Shape: {video_array.shape}")
        print(f"   Dtype: {video_array.dtype}")
        print(f"   Value range: [{video_array.min():.2f}, {video_array.max():.2f}]")
        
        if len(video_array) == 0:
            print("   ❌ Empty video array")
            return
        
        # Analyze first, middle, and last frames
        frame_indices = [0, len(video_array)//2, len(video_array)-1]
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        fig.suptitle(f'Video Analysis: {Path(npy_path).stem}', fontsize=14)
        
        for i, frame_idx in enumerate(frame_indices):
            frame = video_array[frame_idx]
            
            # Ensure frame is in correct format
            if frame.dtype != np.uint8:
                if frame.max() <= 1.0:
                    frame = (frame * 255).astype(np.uint8)
                else:
                    frame = frame.astype(np.uint8)
            
            # Display frame
            axes[i].imshow(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            axes[i].set_title(f'Frame {frame_idx}')
            axes[i].axis('off')
            
            # Add center crosshair
            h, w = frame.shape[:2]
            axes[i].axhline(y=h//2, color='yellow', linestyle='--', alpha=0.7)
            axes[i].axvline(x=w//2, color='yellow', linestyle='--', alpha=0.7)
            
            # Analyze frame content
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            mean_intensity = np.mean(gray)
            std_intensity = np.std(gray)
            
            print(f"   Frame {frame_idx}: mean={mean_intensity:.1f}, std={std_intensity:.1f}")
            
            # Check if frame is too dark, too bright, or low contrast
            if mean_intensity < 30:
                print(f"     ⚠️  Very dark frame")
            elif mean_intensity > 220:
                print(f"     ⚠️  Very bright frame")
            
            if std_intensity < 15:
                print(f"     ⚠️  Low contrast frame")
        
        # Save analysis
        output_path = Path(output_dir) / f"{Path(npy_path).stem}_analysis.png"
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"   ✅ Analysis saved: {output_path}")
        
        # Create a short video preview
        video_path = Path(output_dir) / f"{Path(npy_path).stem}_preview.mp4"
        create_video_preview(video_array, str(video_path))
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error analyzing {npy_path}: {e}")
        return False

def create_video_preview(video_array: np.ndarray, output_path: str):
    """Create a short video preview with analysis overlays"""
    
    if len(video_array) == 0:
        return
    
    h, w = video_array.shape[1:3]
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    
    with cv2.VideoWriter(output_path, fourcc, 10.0, (w, h)) as writer:
        for frame_idx, frame in enumerate(video_array):
            # Ensure frame is uint8
            if frame.dtype != np.uint8:
                if frame.max() <= 1.0:
                    frame = (frame * 255).astype(np.uint8)
                else:
                    frame = frame.astype(np.uint8)
            
            # Add overlays
            display_frame = frame.copy()
            
            # Center crosshair
            cv2.drawMarker(display_frame, (w//2, h//2), (0, 255, 255),
                          markerType=cv2.MARKER_CROSS, markerSize=15, thickness=2)
            
            # Frame number
            cv2.putText(display_frame, f"Frame {frame_idx}", (5, 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Intensity info
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            mean_intensity = np.mean(gray)
            cv2.putText(display_frame, f"Mean: {mean_intensity:.1f}", (5, 45),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            writer.write(display_frame)
    
    print(f"   ✅ Video preview saved: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Debug processed MediaPipe videos")
    parser.add_argument("--processed_dir", required=True,
                       help="Directory containing processed .npy files")
    parser.add_argument("--output_dir", default="debug_analysis",
                       help="Output directory for analysis")
    parser.add_argument("--max_videos", type=int, default=10,
                       help="Maximum number of videos to analyze")
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Find processed videos
    processed_path = Path(args.processed_dir)
    npy_files = list(processed_path.glob("*_processed_mediapipe.npy"))
    
    if not npy_files:
        print(f"❌ No processed .npy files found in {args.processed_dir}")
        return
    
    print(f"🔍 Found {len(npy_files)} processed videos")
    
    # Analyze videos
    analyzed_count = 0
    success_count = 0
    
    for npy_file in npy_files[:args.max_videos]:
        if analyze_video_content(str(npy_file), str(output_dir)):
            success_count += 1
        analyzed_count += 1
    
    print(f"\n🎯 Analysis Summary:")
    print(f"   Videos analyzed: {analyzed_count}")
    print(f"   Successful analyses: {success_count}")
    print(f"   Output directory: {output_dir}")
    
    print(f"\n💡 Next Steps:")
    print(f"   1. Check the analysis images in {output_dir}")
    print(f"   2. Watch the preview videos to see actual crop content")
    print(f"   3. Look for patterns in failed crops:")
    print(f"      - Are lips visible and centered?")
    print(f"      - Are frames too dark/bright/blurry?")
    print(f"      - Are crops showing wrong body parts?")

if __name__ == "__main__":
    main()
