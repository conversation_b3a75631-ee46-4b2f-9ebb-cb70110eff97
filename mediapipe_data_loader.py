#!/usr/bin/env python3
"""
MediaPipe RGB Data Loader
========================

Data loading pipeline for MediaPipe-processed RGB lip crops.
Handles numpy arrays with proper normalization and augmentation.
"""

import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from pathlib import Path
from typing import Tuple, Dict, List, Optional
import json
import random
from sklearn.model_selection import train_test_split
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MediaPipeVideoDataset(Dataset):
    """Dataset for MediaPipe-processed RGB video tensors"""
    
    def __init__(self, 
                 video_files: List[str], 
                 labels: List[int], 
                 phrases: List[str],
                 transform: Optional[transforms.Compose] = None,
                 config: Dict = None):
        """
        Initialize MediaPipe dataset
        
        Args:
            video_files: List of paths to .npy files
            labels: List of integer labels
            phrases: List of phrase strings
            transform: Optional transforms to apply
            config: Configuration dictionary
        """
        self.video_files = video_files
        self.labels = labels
        self.phrases = phrases
        self.transform = transform
        self.config = config or {}
        
        # Validate data consistency
        assert len(video_files) == len(labels) == len(phrases), \
            f"Inconsistent data lengths: {len(video_files)}, {len(labels)}, {len(phrases)}"
        
        logger.info(f"MediaPipe dataset initialized with {len(video_files)} videos")
    
    def __len__(self) -> int:
        return len(self.video_files)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
        """Get a single video sample"""
        video_file = self.video_files[idx]
        label = self.labels[idx]
        phrase = self.phrases[idx]
        
        try:
            # Load MediaPipe-processed video tensor
            video_array = np.load(video_file)  # Shape: [T, H, W, C]
            
            # Validate shape
            expected_shape = (32, 112, 112, 3)
            if video_array.shape != expected_shape:
                logger.warning(f"Unexpected shape {video_array.shape} for {video_file}, expected {expected_shape}")
                # Resize if needed
                video_array = self._resize_video(video_array, expected_shape)
            
            # Convert to torch tensor and rearrange dimensions
            # From [T, H, W, C] to [C, T, H, W] for 3D CNN
            video_tensor = torch.from_numpy(video_array).float()
            video_tensor = video_tensor.permute(3, 0, 1, 2)  # [C, T, H, W]
            
            # Apply transforms if provided
            if self.transform:
                video_tensor = self.transform(video_tensor)
            
            # Create metadata
            metadata = {
                'video_path': video_file,
                'phrase': phrase,
                'label': label,
                'original_shape': video_array.shape
            }
            
            return video_tensor, torch.tensor(label, dtype=torch.long), metadata
            
        except Exception as e:
            logger.error(f"Error loading {video_file}: {e}")
            # Return zero tensor as fallback
            video_tensor = torch.zeros(3, 32, 112, 112)
            metadata = {
                'video_path': video_file,
                'phrase': phrase,
                'label': label,
                'error': str(e)
            }
            return video_tensor, torch.tensor(label, dtype=torch.long), metadata
    
    def _resize_video(self, video_array: np.ndarray, target_shape: Tuple[int, int, int, int]) -> np.ndarray:
        """Resize video array to target shape"""
        import cv2
        
        T_target, H_target, W_target, C_target = target_shape
        T_current, H_current, W_current, C_current = video_array.shape
        
        # Handle temporal dimension
        if T_current != T_target:
            if T_current > T_target:
                # Sample frames evenly
                indices = np.linspace(0, T_current - 1, T_target, dtype=int)
                video_array = video_array[indices]
            else:
                # Repeat last frame
                while video_array.shape[0] < T_target:
                    video_array = np.concatenate([video_array, video_array[-1:]], axis=0)
        
        # Handle spatial dimensions
        if H_current != H_target or W_current != W_target:
            resized_frames = []
            for frame in video_array:
                resized_frame = cv2.resize(frame, (W_target, H_target), interpolation=cv2.INTER_AREA)
                resized_frames.append(resized_frame)
            video_array = np.array(resized_frames)
        
        return video_array

class MediaPipeTransforms:
    """Custom transforms for MediaPipe RGB video data"""
    
    @staticmethod
    def create_transforms(config: Dict, is_training: bool = True) -> transforms.Compose:
        """Create transform pipeline for MediaPipe data"""
        
        transform_list = []
        
        # Normalize to [0, 1] range first (MediaPipe outputs 0-255)
        transform_list.append(transforms.Lambda(lambda x: x / 255.0))
        
        if is_training and config.get('augmentation', {}).get('enabled', False):
            # Training augmentations
            aug_config = config['augmentation']
            
            # Photometric augmentations
            if aug_config.get('brightness_factor', 0) > 0:
                brightness = aug_config['brightness_factor']
                transform_list.append(
                    transforms.Lambda(lambda x: MediaPipeTransforms._adjust_brightness(x, brightness))
                )
            
            if aug_config.get('contrast_factor', 0) > 0:
                contrast = aug_config['contrast_factor']
                transform_list.append(
                    transforms.Lambda(lambda x: MediaPipeTransforms._adjust_contrast(x, contrast))
                )
            
            # Gaussian noise
            if aug_config.get('gaussian_noise_std', 0) > 0:
                noise_std = aug_config['gaussian_noise_std']
                transform_list.append(
                    transforms.Lambda(lambda x: MediaPipeTransforms._add_gaussian_noise(x, noise_std))
                )
        
        # ImageNet normalization (after converting to [0, 1])
        if 'transforms' in config and 'normalize' in config['transforms']:
            norm_config = config['transforms']['normalize']
            mean = norm_config['mean']
            std = norm_config['std']
            transform_list.append(transforms.Normalize(mean=mean, std=std))
        
        return transforms.Compose(transform_list)
    
    @staticmethod
    def _adjust_brightness(video_tensor: torch.Tensor, factor: float) -> torch.Tensor:
        """Adjust brightness of video tensor"""
        brightness_factor = 1.0 + random.uniform(-factor, factor)
        return torch.clamp(video_tensor * brightness_factor, 0, 1)
    
    @staticmethod
    def _adjust_contrast(video_tensor: torch.Tensor, factor: float) -> torch.Tensor:
        """Adjust contrast of video tensor"""
        contrast_factor = 1.0 + random.uniform(-factor, factor)
        mean = video_tensor.mean()
        return torch.clamp((video_tensor - mean) * contrast_factor + mean, 0, 1)
    
    @staticmethod
    def _add_gaussian_noise(video_tensor: torch.Tensor, std: float) -> torch.Tensor:
        """Add Gaussian noise to video tensor"""
        noise = torch.randn_like(video_tensor) * std
        return torch.clamp(video_tensor + noise, 0, 1)

def create_mediapipe_manifest(preprocessed_dir: str, output_path: str) -> str:
    """Create manifest file from MediaPipe preprocessed directory"""
    
    preprocessed_path = Path(preprocessed_dir)
    
    # Find all processed .npy files
    npy_files = list(preprocessed_path.glob("*_processed_mediapipe.npy"))
    
    if not npy_files:
        raise ValueError(f"No MediaPipe processed files found in {preprocessed_dir}")
    
    # Extract information from filenames
    manifest_data = []
    
    for npy_file in npy_files:
        # Parse filename: word__speaker__age__gender__ethnicity__timestamp_processed_mediapipe.npy
        filename = npy_file.stem.replace('_processed_mediapipe', '')
        parts = filename.split('__')
        
        if len(parts) >= 6:
            word = parts[0]
            speaker = parts[1]
            age_group = parts[2]
            gender = parts[3]
            ethnicity = parts[4]
            timestamp = parts[5]
            
            manifest_data.append({
                'video_path': str(npy_file),
                'phrase': word,
                'speaker_id': speaker,
                'age_group': age_group,
                'gender': gender,
                'ethnicity': ethnicity,
                'timestamp': timestamp,
                'filename': filename
            })
        else:
            logger.warning(f"Could not parse filename: {filename}")
    
    # Create DataFrame and save
    manifest_df = pd.DataFrame(manifest_data)
    manifest_df.to_csv(output_path, index=False)
    
    logger.info(f"Created manifest with {len(manifest_df)} videos: {output_path}")
    logger.info(f"Phrases: {sorted(manifest_df['phrase'].unique())}")
    logger.info(f"Speakers: {len(manifest_df['speaker_id'].unique())}")
    
    return output_path

def create_mediapipe_dataloaders(config: Dict, manifest_path: str) -> Tuple[DataLoader, DataLoader, DataLoader, Dict]:
    """Create data loaders for MediaPipe-processed data"""
    
    # Load manifest
    manifest_df = pd.read_csv(manifest_path)
    logger.info(f"Loaded manifest with {len(manifest_df)} videos")
    
    # Create phrase to index mapping
    unique_phrases = sorted(manifest_df['phrase'].unique())
    phrase_to_idx = {phrase: idx for idx, phrase in enumerate(unique_phrases)}
    
    # Convert phrases to labels
    manifest_df['label'] = manifest_df['phrase'].map(phrase_to_idx)
    
    # Check if manifest has pre-defined splits
    if 'split' in manifest_df.columns:
        logger.info("Using pre-defined splits from manifest")
        train_df = manifest_df[manifest_df['split'] == 'train']
        val_df = manifest_df[manifest_df['split'] == 'val']
        test_df = manifest_df[manifest_df['split'] == 'test']

        logger.info(f"Pre-split sizes - Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")

    # Speaker-wise split to prevent data leakage (if enough speakers)
    elif config['data'].get('split_by_speaker', True):
        speakers = manifest_df['speaker_id'].unique()
        min_speakers_needed = 3  # Need at least 3 speakers for train/val/test split

        if len(speakers) >= min_speakers_needed:
            logger.info(f"Using speaker-wise split with {len(speakers)} speakers")

            # Split speakers
            train_speakers, temp_speakers = train_test_split(
                speakers,
                test_size=config['data']['val_ratio'] + config['data']['test_ratio'],
                random_state=config['data']['random_seed']
            )

            val_speakers, test_speakers = train_test_split(
                temp_speakers,
                test_size=config['data']['test_ratio'] / (config['data']['val_ratio'] + config['data']['test_ratio']),
                random_state=config['data']['random_seed']
            )

            # Create splits
            train_df = manifest_df[manifest_df['speaker_id'].isin(train_speakers)]
            val_df = manifest_df[manifest_df['speaker_id'].isin(val_speakers)]
            test_df = manifest_df[manifest_df['speaker_id'].isin(test_speakers)]

    else:
        # Fallback to random split (for small datasets or testing)
        logger.warning(f"Only {len(speakers)} speakers available, using random split instead of speaker-wise split")
        
        # Handle small datasets with special splitting logic
        if len(manifest_df) < 10:
            logger.warning(f"Very small dataset ({len(manifest_df)} videos), using simple split")
            # For very small datasets, use simple indexing
            n_total = len(manifest_df)
            n_train = max(1, int(n_total * 0.6))  # At least 1 for training
            n_val = max(1, int(n_total * 0.2)) if n_total > 2 else 0
            n_test = n_total - n_train - n_val

            # Shuffle the dataframe
            shuffled_df = manifest_df.sample(frac=1, random_state=config['data']['random_seed']).reset_index(drop=True)

            train_df = shuffled_df[:n_train]
            val_df = shuffled_df[n_train:n_train+n_val] if n_val > 0 else pd.DataFrame(columns=manifest_df.columns)
            test_df = shuffled_df[n_train+n_val:] if n_test > 0 else pd.DataFrame(columns=manifest_df.columns)

        else:
            # Normal random split for larger datasets
            train_df, temp_df = train_test_split(
                manifest_df,
                test_size=config['data']['val_ratio'] + config['data']['test_ratio'],
                random_state=config['data']['random_seed'],
                stratify=manifest_df['phrase'] if config['data'].get('stratify_by_phrase', True) else None
            )

            val_df, test_df = train_test_split(
                temp_df,
                test_size=config['data']['test_ratio'] / (config['data']['val_ratio'] + config['data']['test_ratio']),
                random_state=config['data']['random_seed'],
                stratify=temp_df['phrase'] if config['data'].get('stratify_by_phrase', True) else None
            )
    
    # Create transforms
    train_transform = MediaPipeTransforms.create_transforms(config, is_training=True)
    val_transform = MediaPipeTransforms.create_transforms(config, is_training=False)
    
    # Create datasets
    train_dataset = MediaPipeVideoDataset(
        train_df['video_path'].tolist(),
        train_df['label'].tolist(),
        train_df['phrase'].tolist(),
        transform=train_transform,
        config=config
    )
    
    val_dataset = MediaPipeVideoDataset(
        val_df['video_path'].tolist(),
        val_df['label'].tolist(),
        val_df['phrase'].tolist(),
        transform=val_transform,
        config=config
    )
    
    test_dataset = MediaPipeVideoDataset(
        test_df['video_path'].tolist(),
        test_df['label'].tolist(),
        test_df['phrase'].tolist(),
        transform=val_transform,
        config=config
    )
    
    # Create data loaders
    batch_size = config['training']['batch_size']
    num_workers = config['hardware']['num_workers']
    pin_memory = config['hardware']['pin_memory']
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=True  # Ensure consistent batch sizes
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory
    ) if len(val_dataset) > 0 else None
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory
    ) if len(test_dataset) > 0 else None
    
    # Create info dictionary
    data_info = {
        'train_size': len(train_dataset),
        'val_size': len(val_dataset),
        'test_size': len(test_dataset),
        'num_classes': len(unique_phrases),
        'phrase_to_idx': phrase_to_idx,
        'phrases': unique_phrases,
        'train_speakers': len(train_df['speaker_id'].unique()) if 'speaker_id' in train_df.columns else 0,
        'val_speakers': len(val_df['speaker_id'].unique()) if 'speaker_id' in val_df.columns else 0,
        'test_speakers': len(test_df['speaker_id'].unique()) if 'speaker_id' in test_df.columns else 0
    }
    
    logger.info(f"✅ MediaPipe data loaders created successfully")
    logger.info(f"📊 Train: {data_info['train_size']} videos ({data_info['train_speakers']} speakers)")
    logger.info(f"📊 Val: {data_info['val_size']} videos ({data_info['val_speakers']} speakers)")
    logger.info(f"📊 Test: {data_info['test_size']} videos ({data_info['test_speakers']} speakers)")
    logger.info(f"📊 Classes: {data_info['num_classes']} - {data_info['phrases']}")
    
    return train_loader, val_loader, test_loader, data_info

def main():
    """Test the MediaPipe data loader"""
    import yaml
    
    # Load config
    with open('configs/mediapipe_rgb_training.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Create manifest from test data
    manifest_path = create_mediapipe_manifest(
        "data/preprocessed_mediapipe_test",
        "data/mediapipe_test_manifest.csv"
    )
    
    # Create data loaders
    train_loader, val_loader, test_loader, data_info = create_mediapipe_dataloaders(
        config, manifest_path
    )
    
    # Test first batch
    if train_loader:
        for videos, labels, metadata in train_loader:
            print(f"📊 Batch shape: {videos.shape}")  # Should be [B, C, T, H, W]
            print(f"📊 Label shape: {labels.shape}")
            print(f"📊 Video range: [{videos.min():.3f}, {videos.max():.3f}]")
            print(f"📊 Labels: {labels.tolist()}")
            break

if __name__ == "__main__":
    main()
