#!/usr/bin/env python3
"""
Diagnose Original Videos
=======================

Check what's actually in the original WebM videos to understand
why MediaPipe face detection is failing.
"""

import cv2
import numpy as np
import mediapipe as mp
from pathlib import Path
import matplotlib.pyplot as plt

def analyze_original_video(video_path: str):
    """Analyze what's in the original video"""
    print(f"\n🔍 Analyzing original video: {Path(video_path).name}")
    
    # Initialize MediaPipe
    mp_face_detection = mp.solutions.face_detection
    mp_face_mesh = mp.solutions.face_mesh
    
    cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
    if not cap.isOpened():
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("❌ Cannot open video")
            return
    
    # Get video info
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"📹 Video info: {total_frames} frames, {fps:.1f} fps, {width}x{height}")
    
    # Read first few frames
    frames_to_check = min(10, total_frames) if total_frames > 0 else 10
    faces_detected = 0
    
    with mp_face_detection.FaceDetection(model_selection=1, min_detection_confidence=0.3) as face_detection, \
         mp_face_mesh.FaceMesh(static_image_mode=True, max_num_faces=1, min_detection_confidence=0.3) as face_mesh:
        
        for frame_idx in range(frames_to_check):
            ret, frame = cap.read()
            if not ret:
                break
            
            # Convert to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Try face detection
            face_results = face_detection.process(rgb_frame)
            mesh_results = face_mesh.process(rgb_frame)
            
            face_detected = False
            mesh_detected = False
            
            if face_results.detections:
                face_detected = True
                detection = face_results.detections[0]
                bbox = detection.location_data.relative_bounding_box
                x = int(bbox.xmin * width)
                y = int(bbox.ymin * height)
                w = int(bbox.width * width)
                h = int(bbox.height * height)
                print(f"   Frame {frame_idx}: Face detected at ({x}, {y}, {w}, {h})")
            
            if mesh_results.multi_face_landmarks:
                mesh_detected = True
                print(f"   Frame {frame_idx}: Face mesh detected with {len(mesh_results.multi_face_landmarks[0].landmark)} landmarks")
            
            if face_detected or mesh_detected:
                faces_detected += 1
                
                # Save first frame with detection for inspection
                if faces_detected == 1:
                    debug_frame = frame.copy()
                    if face_detected:
                        cv2.rectangle(debug_frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
                    cv2.imwrite(f"debug_original_{Path(video_path).stem}.jpg", debug_frame)
                    print(f"   Saved debug frame: debug_original_{Path(video_path).stem}.jpg")
            else:
                print(f"   Frame {frame_idx}: No face detected")
    
    cap.release()
    
    detection_rate = faces_detected / frames_to_check if frames_to_check > 0 else 0
    print(f"📊 Detection rate: {faces_detected}/{frames_to_check} = {detection_rate:.1%}")
    
    return detection_rate > 0

def compare_original_vs_processed(original_path: str, processed_path: str):
    """Compare original video with processed output"""
    print(f"\n🔄 Comparing original vs processed:")
    print(f"   Original: {Path(original_path).name}")
    print(f"   Processed: {Path(processed_path).name}")
    
    # Load processed video
    try:
        processed_array = np.load(processed_path)
        print(f"   Processed shape: {processed_array.shape}")
        print(f"   Processed range: [{processed_array.min()}, {processed_array.max()}]")
        
        # Show first frame of processed
        if len(processed_array) > 0:
            first_frame = processed_array[0]
            if first_frame.dtype != np.uint8:
                first_frame = (first_frame * 255).astype(np.uint8)
            
            cv2.imwrite(f"debug_processed_{Path(processed_path).stem}.jpg", first_frame)
            print(f"   Saved processed frame: debug_processed_{Path(processed_path).stem}.jpg")
            
            # Check if frame is mostly black/empty
            mean_intensity = np.mean(first_frame)
            print(f"   Processed frame mean intensity: {mean_intensity:.1f}")
            
            if mean_intensity < 30:
                print("   ⚠️  Processed frame appears very dark/empty")
            
    except Exception as e:
        print(f"   ❌ Error loading processed file: {e}")

def main():
    # Test original videos
    test_videos = [
        "data/top 5 dataset 30.8.25/doctor__useruser01__18to39__male__not_specified__20250827T052630.webm",
        "data/top 5 dataset 30.8.25/glasses__useruser01__65plus__female__caucasian__20250827T055622.webm",
        "data/top 5 dataset 30.8.25/pillow__useruser01__18to39__male__not_specified__20250820T091743.webm"
    ]
    
    print("🚀 Original Video Diagnosis")
    print("=" * 60)
    
    working_videos = 0
    
    for video_path in test_videos:
        if Path(video_path).exists():
            if analyze_original_video(video_path):
                working_videos += 1
                
                # Compare with processed version if it exists
                processed_name = Path(video_path).stem + "_processed_v2.npy"
                processed_path = f"data/processed_v2/{processed_name}"
                
                if Path(processed_path).exists():
                    compare_original_vs_processed(video_path, processed_path)
        else:
            print(f"❌ Video not found: {video_path}")
    
    print(f"\n🎯 Summary:")
    print(f"   Videos with face detection: {working_videos}/{len(test_videos)}")
    
    if working_videos == 0:
        print(f"\n🚨 CRITICAL FINDING:")
        print(f"   NO faces detected in original videos!")
        print(f"   This explains why MediaPipe preprocessing is failing.")
        print(f"   The original videos might not contain proper face regions.")
    else:
        print(f"\n💡 Next steps:")
        print(f"   Check the debug images to see what's being detected vs processed")

if __name__ == "__main__":
    main()
