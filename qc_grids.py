#!/usr/bin/env python3
"""
Quick Visual QC Grids for MediaPipe Processed Videos
===================================================

Creates visual grids showing random samples per class to verify
that lips are properly centered and visible in the processed crops.

Usage:
    python qc_grids.py --processed_dir data/preprocessed_mediapipe_full --per_class 16 --out qc/grids
"""

import os
import random
import argparse
import numpy as np
import pandas as pd
import cv2
from pathlib import Path

def load_processed_files(processed_dir):
    """Load all processed .npy files and extract metadata from filenames"""
    processed_path = Path(processed_dir)
    npy_files = list(processed_path.glob("*_processed_mediapipe.npy"))
    
    if not npy_files:
        raise SystemExit(f"No processed .npy files found in {processed_dir}")
    
    # Extract metadata from filenames
    data = []
    for npy_file in npy_files:
        # Parse filename: word__speaker__age__gender__ethnicity__timestamp_processed_mediapipe.npy
        filename = npy_file.stem.replace('_processed_mediapipe', '')
        parts = filename.split('__')
        
        if len(parts) >= 6:
            word = parts[0]
            speaker = parts[1]
            age_group = parts[2]
            gender = parts[3]
            ethnicity = parts[4]
            timestamp = parts[5]
            
            data.append({
                'video_path': str(npy_file),
                'phrase': word,
                'speaker': speaker,
                'age_group': age_group,
                'gender': gender,
                'ethnicity': ethnicity,
                'timestamp': timestamp
            })
        else:
            print(f"[WARN] Could not parse filename: {filename}")
    
    df = pd.DataFrame(data)
    print(f"Loaded {len(df)} processed videos")
    print(f"Classes found: {sorted(df['phrase'].unique())}")
    print(f"Speakers: {len(df['speaker'].unique())}")
    
    return df

def tile_images(imgs, rows, cols, pad=2):
    """Create a tiled grid from list of images"""
    if not imgs:
        return np.zeros((100, 100, 3), dtype=np.uint8)
    
    h, w, c = imgs[0].shape
    grid = np.full(((h+pad)*rows-pad, (w+pad)*cols-pad, c), 255, dtype=np.uint8)
    
    k = 0
    for r in range(rows):
        for c_ in range(cols):
            if k >= len(imgs):
                break
            y, x = r*(h+pad), c_*(w+pad)
            grid[y:y+h, x:x+w] = imgs[k]
            k += 1
    
    return grid

def first_frame_preview(npy_path):
    """Extract middle frame from processed video and add center marker"""
    try:
        arr = np.load(npy_path)  # Shape: [T, 112, 112, 3]
        
        if arr.shape[0] == 0:
            print(f"[WARN] Empty video array: {npy_path}")
            return None
        
        # Get middle frame
        middle_idx = len(arr) // 2
        frame = arr[middle_idx].copy()  # Shape: [112, 112, 3]
        
        # Convert to BGR for OpenCV
        if frame.dtype != np.uint8:
            frame = (frame * 255).astype(np.uint8)
        
        # Add center crosshair to check centering
        h, w, _ = frame.shape
        center_x, center_y = w // 2, h // 2
        
        # Draw green crosshair at center
        cv2.drawMarker(frame, (center_x, center_y), (0, 255, 0), 
                      markerType=cv2.MARKER_CROSS, markerSize=10, thickness=2)
        
        # Add small info text
        cv2.putText(frame, f"F{middle_idx}/{len(arr)}", (2, 12), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        return frame
        
    except Exception as e:
        print(f"[ERROR] Failed to process {npy_path}: {e}")
        return None

def create_class_grid(class_name, video_paths, per_class, output_dir):
    """Create visual grid for one class"""
    print(f"Processing class: {class_name} ({len(video_paths)} videos available)")
    
    # Sample videos
    sample_paths = random.sample(video_paths, min(per_class, len(video_paths)))
    
    # Extract frames
    thumbnails = []
    failed_count = 0
    
    for path in sample_paths:
        frame = first_frame_preview(path)
        if frame is not None:
            thumbnails.append(frame)
        else:
            failed_count += 1
    
    if not thumbnails:
        print(f"[WARN] No valid thumbnails for class {class_name}")
        return False
    
    # Calculate grid dimensions
    n_thumbs = len(thumbnails)
    grid_side = int(np.ceil(np.sqrt(n_thumbs)))
    
    # Create grid
    grid = tile_images(thumbnails, grid_side, grid_side, pad=3)
    
    # Add title
    title_height = 30
    title_img = np.full((title_height, grid.shape[1], 3), 50, dtype=np.uint8)
    title_text = f"{class_name.upper()} - {n_thumbs} samples"
    if failed_count > 0:
        title_text += f" ({failed_count} failed)"
    
    cv2.putText(title_img, title_text, (10, 20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # Combine title and grid
    final_grid = np.vstack([title_img, grid])
    
    # Save
    output_path = Path(output_dir) / f"{class_name}_grid.jpg"
    cv2.imwrite(str(output_path), final_grid)
    
    print(f"✅ Saved {output_path} ({n_thumbs} samples, {failed_count} failed)")
    return True

def main():
    parser = argparse.ArgumentParser(description="Create visual QC grids for MediaPipe processed videos")
    parser.add_argument("--processed_dir", required=True, 
                       help="Directory containing processed .npy files")
    parser.add_argument("--per_class", type=int, default=16,
                       help="Number of samples per class")
    parser.add_argument("--out", default="qc/grids",
                       help="Output directory for grid images")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.out, exist_ok=True)
    
    # Load processed files
    try:
        df = load_processed_files(args.processed_dir)
    except Exception as e:
        print(f"Error loading processed files: {e}")
        return
    
    # Get unique classes
    classes = sorted(df['phrase'].unique())
    print(f"\nCreating grids for {len(classes)} classes: {classes}")
    
    # Create grid for each class
    success_count = 0
    for class_name in classes:
        class_videos = df[df['phrase'] == class_name]['video_path'].tolist()
        
        if create_class_grid(class_name, class_videos, args.per_class, args.out):
            success_count += 1
    
    print(f"\n🎯 QC Grid Summary:")
    print(f"   Classes processed: {success_count}/{len(classes)}")
    print(f"   Output directory: {args.out}")
    print(f"\n📋 What to look for in the grids:")
    print(f"   ✅ Lips clearly visible and centered (near green crosshair)")
    print(f"   ✅ Consistent crop quality across samples")
    print(f"   ❌ Many frames showing chin/nose/blank background")
    print(f"   ❌ Lips cut off or positioned at edges")
    
    # Create summary image with all classes
    if success_count > 0:
        print(f"\n💡 Open the grid images in {args.out} to visually inspect crop quality")
        print(f"   If crops look good → proceed with training")
        print(f"   If crops look poor → investigate failed videos or adjust MediaPipe parameters")

if __name__ == "__main__":
    main()
