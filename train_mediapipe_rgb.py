#!/usr/bin/env python3
"""
MediaPipe RGB Training Script
============================

Training script for ICU lipreading using MediaPipe-processed RGB lip crops.
Uses R3D-18 backbone with pretrained ImageNet weights for optimal performance.
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import yaml
import json
import numpy as np
from pathlib import Path
import logging
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report
import matplotlib.pyplot as plt
import seaborn as sns

# Import our custom modules
from mediapipe_data_loader import create_mediapipe_dataloaders, create_mediapipe_manifest
import torchvision.models.video as video_models

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class R3D18Classifier(nn.Module):
    """R3D-18 based classifier for lipreading"""
    
    def __init__(self, num_classes: int = 5, pretrained: bool = True, dropout: float = 0.3):
        super(R3D18Classifier, self).__init__()
        
        # Load pretrained R3D-18
        self.backbone = video_models.r3d_18(pretrained=pretrained)
        
        # Get the number of features from the final layer
        num_features = self.backbone.fc.in_features
        
        # Replace the final classifier
        self.backbone.fc = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(num_features, 512),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(512, num_classes)
        )
        
        self.num_classes = num_classes
        
    def forward(self, x):
        # Input shape: [B, C, T, H, W]
        return self.backbone(x)
    
    def freeze_backbone(self):
        """Freeze backbone parameters for transfer learning"""
        for param in self.backbone.parameters():
            param.requires_grad = False
        # Unfreeze the final classifier
        for param in self.backbone.fc.parameters():
            param.requires_grad = True
    
    def unfreeze_backbone(self):
        """Unfreeze backbone parameters for fine-tuning"""
        for param in self.backbone.parameters():
            param.requires_grad = True

class MediaPipeTrainer:
    """Trainer for MediaPipe RGB lipreading model"""
    
    def __init__(self, config_path: str):
        """Initialize trainer with configuration"""
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
        # Create output directory
        self.output_dir = Path(self.config['checkpoints']['save_dir'])
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save config
        with open(self.output_dir / 'config.yaml', 'w') as f:
            yaml.dump(self.config, f, indent=2)
        
        # Initialize model, data, optimizer
        self.model = None
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        
        # Training state
        self.current_epoch = 0
        self.best_val_accuracy = 0.0
        self.training_history = {
            'train_loss': [], 'train_acc': [],
            'val_loss': [], 'val_acc': [],
            'learning_rates': []
        }
    
    def setup_data(self):
        """Setup data loaders"""
        logger.info("Setting up data loaders...")
        
        # Create manifest if it doesn't exist
        preprocessed_dir = self.config['data']['preprocessed_data_dir']
        manifest_path = self.config['data']['manifest_path']
        
        if not Path(manifest_path).exists():
            logger.info(f"Creating manifest from {preprocessed_dir}")
            create_mediapipe_manifest(preprocessed_dir, manifest_path)
        
        # Create data loaders
        self.train_loader, self.val_loader, self.test_loader, self.data_info = create_mediapipe_dataloaders(
            self.config, manifest_path
        )
        
        logger.info(f"Data setup complete:")
        logger.info(f"  Train: {self.data_info['train_size']} videos")
        logger.info(f"  Val: {self.data_info['val_size']} videos") 
        logger.info(f"  Test: {self.data_info['test_size']} videos")
        logger.info(f"  Classes: {self.data_info['phrases']}")
    
    def setup_model(self):
        """Setup model"""
        logger.info("Setting up model...")
        
        self.model = R3D18Classifier(
            num_classes=self.data_info['num_classes'],
            pretrained=self.config['model']['pretrained'],
            dropout=self.config['model']['dropout']
        ).to(self.device)
        
        # Freeze backbone initially if specified
        if self.config['model']['freeze_backbone']:
            self.model.freeze_backbone()
            logger.info("Backbone frozen for initial training")
        
        logger.info(f"Model created with {sum(p.numel() for p in self.model.parameters())} parameters")
        logger.info(f"Trainable parameters: {sum(p.numel() for p in self.model.parameters() if p.requires_grad)}")
    
    def setup_training(self):
        """Setup optimizer, scheduler, and loss function"""
        logger.info("Setting up training components...")
        
        # Optimizer
        if self.config['model']['freeze_backbone']:
            # Only optimize classifier parameters initially
            params = [p for p in self.model.parameters() if p.requires_grad]
        else:
            params = self.model.parameters()
        
        self.optimizer = optim.AdamW(
            params,
            lr=self.config['training']['learning_rate'],
            weight_decay=self.config['training']['weight_decay'],
            betas=self.config['training']['betas']
        )
        
        # Scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=self.config['training']['num_epochs'],
            eta_min=self.config['training']['min_lr']
        )
        
        # Loss function
        if self.config['regularization']['class_weights'] == 'balanced':
            # Calculate class weights
            class_counts = np.bincount([self.train_loader.dataset.labels[i] for i in range(len(self.train_loader.dataset))])
            class_weights = len(self.train_loader.dataset) / (len(class_counts) * class_counts)
            class_weights = torch.FloatTensor(class_weights).to(self.device)
            logger.info(f"Using balanced class weights: {class_weights}")
        else:
            class_weights = None
        
        self.criterion = nn.CrossEntropyLoss(
            weight=class_weights,
            label_smoothing=self.config['regularization']['label_smoothing']
        )
    
    def train_epoch(self):
        """Train for one epoch"""
        self.model.train()
        total_loss = 0.0
        all_preds = []
        all_labels = []
        
        pbar = tqdm(self.train_loader, desc=f"Epoch {self.current_epoch+1}")
        
        for batch_idx, (videos, labels, metadata) in enumerate(pbar):
            videos = videos.to(self.device)
            labels = labels.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(videos)
            loss = self.criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            if self.config['training']['grad_clip_norm'] > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    self.config['training']['grad_clip_norm']
                )
            
            self.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            preds = torch.argmax(outputs, dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f"{loss.item():.4f}",
                'Acc': f"{accuracy_score(all_labels, all_preds):.3f}"
            })
        
        avg_loss = total_loss / len(self.train_loader)
        accuracy = accuracy_score(all_labels, all_preds)
        
        return avg_loss, accuracy
    
    def validate(self):
        """Validate model"""
        if self.val_loader is None:
            return 0.0, 0.0
        
        self.model.eval()
        total_loss = 0.0
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for videos, labels, metadata in tqdm(self.val_loader, desc="Validating"):
                videos = videos.to(self.device)
                labels = labels.to(self.device)
                
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                
                total_loss += loss.item()
                preds = torch.argmax(outputs, dim=1)
                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        avg_loss = total_loss / len(self.val_loader)
        accuracy = accuracy_score(all_labels, all_preds)
        
        return avg_loss, accuracy
    
    def save_checkpoint(self, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_accuracy': self.best_val_accuracy,
            'training_history': self.training_history,
            'config': self.config,
            'data_info': self.data_info
        }
        
        # Save latest checkpoint
        torch.save(checkpoint, self.output_dir / 'latest_checkpoint.pth')
        
        # Save best checkpoint
        if is_best:
            torch.save(checkpoint, self.output_dir / 'best_checkpoint.pth')
            logger.info(f"New best model saved with validation accuracy: {self.best_val_accuracy:.4f}")
    
    def train(self):
        """Main training loop"""
        logger.info("Starting training...")
        
        # Setup everything
        self.setup_data()
        self.setup_model()
        self.setup_training()
        
        # Training loop
        for epoch in range(self.config['training']['num_epochs']):
            self.current_epoch = epoch
            
            # Unfreeze backbone after specified epochs
            if (self.config['model']['freeze_backbone'] and 
                epoch == self.config['model']['freeze_epochs']):
                self.model.unfreeze_backbone()
                logger.info("Backbone unfrozen for fine-tuning")
                
                # Update optimizer to include all parameters
                self.optimizer = optim.AdamW(
                    self.model.parameters(),
                    lr=self.config['training']['backbone_lr'],
                    weight_decay=self.config['training']['weight_decay']
                )
            
            # Train and validate
            train_loss, train_acc = self.train_epoch()
            val_loss, val_acc = self.validate()
            
            # Update scheduler
            self.scheduler.step()
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # Update history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)
            self.training_history['learning_rates'].append(current_lr)
            
            # Check for best model
            is_best = val_acc > self.best_val_accuracy
            if is_best:
                self.best_val_accuracy = val_acc
            
            # Save checkpoint
            if (epoch + 1) % self.config['training']['save_every_n_epochs'] == 0:
                self.save_checkpoint(is_best)
            
            # Log progress
            logger.info(f"Epoch {epoch+1}/{self.config['training']['num_epochs']}")
            logger.info(f"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
            logger.info(f"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
            logger.info(f"  LR: {current_lr:.6f}, Best Val Acc: {self.best_val_accuracy:.4f}")
            
            # Early stopping check
            if self.config['training']['early_stopping']['enabled']:
                # Simple early stopping based on validation accuracy
                if len(self.training_history['val_acc']) >= self.config['training']['early_stopping']['patience']:
                    recent_accs = self.training_history['val_acc'][-self.config['training']['early_stopping']['patience']:]
                    if max(recent_accs) - min(recent_accs) < self.config['training']['early_stopping']['min_delta']:
                        logger.info("Early stopping triggered")
                        break
        
        # Final save
        self.save_checkpoint(is_best=False)
        
        # Save training history
        with open(self.output_dir / 'training_history.json', 'w') as f:
            json.dump(self.training_history, f, indent=2)
        
        logger.info(f"Training completed! Best validation accuracy: {self.best_val_accuracy:.4f}")
        
        return self.best_val_accuracy

def main():
    """Main training function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Train MediaPipe RGB lipreading model")
    parser.add_argument("--config", default="configs/mediapipe_rgb_training.yaml", 
                       help="Path to config file")
    
    args = parser.parse_args()
    
    # Create trainer and start training
    trainer = MediaPipeTrainer(args.config)
    best_accuracy = trainer.train()
    
    print(f"\n🎯 Training Summary:")
    print(f"   Best validation accuracy: {best_accuracy:.4f}")
    print(f"   Target achieved: {'✅' if best_accuracy >= 0.90 else '❌'}")
    print(f"   Model saved to: {trainer.output_dir}")

if __name__ == "__main__":
    main()
