#!/usr/bin/env python3
"""
MediaPipe-based Lip ROI Extraction Pipeline
==========================================

This script implements proper landmark-based lip region extraction using MediaPipe Face Mesh.
Designed to replace the current poor-quality ROI extraction that's causing training failures.

Key Features:
- MediaPipe Face Mesh for robust facial landmark detection
- Mouth ROI extraction using lip landmarks (indices 61-88)
- 15-20px padding around detected mouth region for context
- Consistent 112×112 RGB output format
- Robust error handling for edge cases
- Processing statistics and quality reporting

Usage:
    python preprocess_lips_mediapipe.py --input_dir "data/top 5 dataset 30.8.25" --output_dir "data/preprocessed_mediapipe" --sample_size 10
"""

import os
import cv2
import numpy as np
import mediapipe as mp
import argparse
import json
from pathlib import Path
from typing import Tuple, Optional, List, Dict, Any
import logging
from tqdm import tqdm
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MediaPipeLipExtractor:
    """MediaPipe-based lip region extractor with robust error handling"""
    
    def __init__(self, padding: int = 35, target_size: Tuple[int, int] = (112, 112)):
        """
        Initialize MediaPipe Face Mesh for lip detection
        
        Args:
            padding: Padding around mouth region in pixels (15-20px recommended)
            target_size: Output size for cropped lip regions
        """
        self.padding = padding
        self.target_size = target_size
        
        # Initialize MediaPipe Face Mesh with optimized settings for better detection
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.2,  # Even lower confidence for challenging videos
            min_tracking_confidence=0.2
        )
        
        # Lip landmark indices (MediaPipe Face Mesh)
        # Outer lip contour: 61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318
        # Inner lip contour: 78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308
        # Combined mouth region landmarks (61-88 covers the main mouth area)
        self.mouth_landmarks = [
            61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,  # Outer lip
            78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308,         # Inner lip
            13, 82, 81, 80, 78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415, 310, 311, 312, 13  # Extended mouth
        ]
        
        # Remove duplicates and sort
        self.mouth_landmarks = sorted(list(set(self.mouth_landmarks)))
        
        logger.info(f"MediaPipe Lip Extractor initialized - Padding: {padding}px, Target size: {target_size}")
    
    def extract_mouth_roi(self, frame: np.ndarray) -> Tuple[Optional[np.ndarray], Dict[str, Any]]:
        """
        Extract mouth ROI from a single frame using MediaPipe landmarks
        
        Args:
            frame: Input frame (BGR format from OpenCV)
            
        Returns:
            Tuple of (cropped_mouth_region, metadata)
            cropped_mouth_region: 112x112x3 RGB array or None if failed
            metadata: Dictionary with processing info
        """
        metadata = {
            'success': False,
            'error': None,
            'landmarks_detected': False,
            'mouth_bbox': None,
            'confidence': 0.0
        }
        
        try:
            # Convert BGR to RGB for MediaPipe
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w = frame.shape[:2]
            
            # Process frame with MediaPipe
            results = self.face_mesh.process(rgb_frame)
            
            if not results.multi_face_landmarks:
                metadata['error'] = 'No face detected'
                return None, metadata
            
            # Get the first (and should be only) face
            face_landmarks = results.multi_face_landmarks[0]
            metadata['landmarks_detected'] = True
            
            # Extract mouth landmark coordinates
            mouth_points = []
            for idx in self.mouth_landmarks:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = int(landmark.x * w)
                    y = int(landmark.y * h)
                    mouth_points.append((x, y))
            
            if len(mouth_points) < 10:  # Need minimum landmarks for reliable ROI
                metadata['error'] = f'Insufficient mouth landmarks: {len(mouth_points)}'
                return None, metadata
            
            # Calculate bounding box around mouth landmarks
            mouth_points = np.array(mouth_points)
            x_min, y_min = np.min(mouth_points, axis=0)
            x_max, y_max = np.max(mouth_points, axis=0)
            
            # Add padding
            x_min = max(0, x_min - self.padding)
            y_min = max(0, y_min - self.padding)
            x_max = min(w, x_max + self.padding)
            y_max = min(h, y_max + self.padding)
            
            # Ensure minimum size
            mouth_width = x_max - x_min
            mouth_height = y_max - y_min
            
            if mouth_width < 30 or mouth_height < 20:
                metadata['error'] = f'Mouth region too small: {mouth_width}x{mouth_height}'
                return None, metadata
            
            # Extract and resize mouth region
            mouth_crop = frame[y_min:y_max, x_min:x_max]
            
            if mouth_crop.size == 0:
                metadata['error'] = 'Empty crop region'
                return None, metadata
            
            # Resize to target size and convert to RGB
            mouth_resized = cv2.resize(mouth_crop, self.target_size, interpolation=cv2.INTER_AREA)
            mouth_rgb = cv2.cvtColor(mouth_resized, cv2.COLOR_BGR2RGB)
            
            # Update metadata
            metadata.update({
                'success': True,
                'mouth_bbox': (x_min, y_min, x_max, y_max),
                'original_size': (mouth_width, mouth_height),
                'confidence': 0.8  # MediaPipe doesn't provide direct confidence for landmarks
            })
            
            return mouth_rgb, metadata
            
        except Exception as e:
            metadata['error'] = f'Processing error: {str(e)}'
            logger.error(f"Error processing frame: {e}")
            return None, metadata
    
    def process_video(self, video_path: str, max_frames: int = 32) -> Tuple[Optional[np.ndarray], Dict[str, Any]]:
        """
        Process entire video and extract mouth ROI from multiple frames

        Args:
            video_path: Path to input video file
            max_frames: Maximum number of frames to extract

        Returns:
            Tuple of (video_tensor, processing_stats)
            video_tensor: Shape [T, H, W, C] where T=max_frames, H=W=112, C=3
            processing_stats: Dictionary with processing statistics
        """
        stats = {
            'success': False,
            'total_frames': 0,
            'processed_frames': 0,
            'failed_frames': 0,
            'success_rate': 0.0,
            'errors': [],
            'video_path': video_path
        }

        try:
            # Try different backends for WebM support
            cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
            if not cap.isOpened():
                # Fallback to default backend
                cap = cv2.VideoCapture(video_path)
                if not cap.isOpened():
                    stats['errors'].append('Could not open video file with any backend')
                    return None, stats

            # Get total frames - handle negative values from WebM
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            if total_frames <= 0:
                # For WebM files, count frames by reading through the video
                logger.info(f"Invalid frame count ({total_frames}), counting frames manually...")
                frame_count = 0
                while True:
                    ret, _ = cap.read()
                    if not ret:
                        break
                    frame_count += 1
                    if frame_count > 500:  # Reasonable limit for lipreading videos
                        break
                total_frames = frame_count
                # Reopen video to reset position (seeking doesn't work reliably with WebM)
                cap.release()
                cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
                if not cap.isOpened():
                    cap = cv2.VideoCapture(video_path)

            stats['total_frames'] = total_frames

            if total_frames == 0:
                stats['errors'].append('Video has no readable frames')
                return None, stats
            
            # Calculate frame indices to sample
            if total_frames <= max_frames:
                frame_indices = list(range(total_frames))
            else:
                # Evenly sample frames across the video
                frame_indices = np.linspace(0, total_frames - 1, max_frames, dtype=int).tolist()

            processed_crops = []

            # For WebM files, sometimes seeking doesn't work well, so read sequentially
            current_frame = 0
            target_frame_set = set(frame_indices)

            while current_frame < total_frames and len(processed_crops) < max_frames:
                ret, frame = cap.read()

                if not ret:
                    break

                # Process this frame if it's one we want
                if current_frame in target_frame_set:
                    # Extract mouth ROI
                    mouth_crop, metadata = self.extract_mouth_roi(frame)

                    if mouth_crop is not None and mouth_crop.size > 0:
                        processed_crops.append(mouth_crop)
                        stats['processed_frames'] += 1
                    else:
                        stats['failed_frames'] += 1
                        if metadata.get('error'):
                            stats['errors'].append(f"Frame {current_frame}: {metadata['error']}")

                current_frame += 1

            cap.release()
            
            if len(processed_crops) == 0:
                stats['errors'].append('No frames could be processed')
                return None, stats
            
            # Pad or trim to exact frame count
            while len(processed_crops) < max_frames:
                # Repeat last frame if we have fewer frames than needed
                processed_crops.append(processed_crops[-1])
            
            processed_crops = processed_crops[:max_frames]  # Trim if we have more
            
            # Convert to numpy array
            video_tensor = np.array(processed_crops)  # Shape: [T, H, W, C]
            
            stats.update({
                'success': True,
                'success_rate': stats['processed_frames'] / len(frame_indices) if frame_indices else 0.0,
                'output_shape': video_tensor.shape
            })
            
            return video_tensor, stats
            
        except Exception as e:
            stats['errors'].append(f'Video processing error: {str(e)}')
            logger.error(f"Error processing video {video_path}: {e}")
            return None, stats
    
    def __del__(self):
        """Cleanup MediaPipe resources"""
        if hasattr(self, 'face_mesh'):
            self.face_mesh.close()

def process_dataset(input_dir: str, output_dir: str, sample_size: Optional[int] = None) -> Dict[str, Any]:
    """
    Process entire dataset with MediaPipe lip extraction
    
    Args:
        input_dir: Directory containing webm video files
        output_dir: Directory to save processed videos
        sample_size: If specified, only process this many videos (for testing)
        
    Returns:
        Processing report dictionary
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Find all webm files
    video_files = list(input_path.glob("*.webm"))
    
    if sample_size:
        video_files = video_files[:sample_size]
    
    logger.info(f"Found {len(video_files)} video files to process")
    
    # Initialize extractor
    extractor = MediaPipeLipExtractor(padding=18, target_size=(112, 112))
    
    # Processing statistics
    report = {
        'total_videos': len(video_files),
        'successful_videos': 0,
        'failed_videos': 0,
        'processing_errors': [],
        'success_rate': 0.0,
        'output_directory': str(output_path),
        'processed_files': []
    }
    
    # Process each video
    for video_file in tqdm(video_files, desc="Processing videos"):
        try:
            # Process video
            video_tensor, stats = extractor.process_video(str(video_file), max_frames=32)
            
            if video_tensor is not None:
                # Save processed video as numpy array
                output_file = output_path / f"{video_file.stem}_processed_mediapipe.npy"
                np.save(output_file, video_tensor)
                
                # Save metadata
                metadata_file = output_path / f"{video_file.stem}_metadata.json"
                with open(metadata_file, 'w') as f:
                    json.dump(stats, f, indent=2)
                
                report['successful_videos'] += 1
                report['processed_files'].append({
                    'input_file': str(video_file),
                    'output_file': str(output_file),
                    'metadata_file': str(metadata_file),
                    'stats': stats
                })
                
                logger.info(f"✅ Processed: {video_file.name} -> {output_file.name}")
            else:
                report['failed_videos'] += 1
                report['processing_errors'].append({
                    'file': str(video_file),
                    'errors': stats['errors']
                })
                logger.warning(f"❌ Failed: {video_file.name} - {stats['errors']}")
                
        except Exception as e:
            report['failed_videos'] += 1
            error_msg = f"Exception processing {video_file}: {str(e)}"
            report['processing_errors'].append({
                'file': str(video_file),
                'errors': [error_msg]
            })
            logger.error(error_msg)
    
    # Calculate final statistics
    report['success_rate'] = report['successful_videos'] / report['total_videos'] if report['total_videos'] > 0 else 0.0
    
    # Save processing report
    report_file = output_path / "processing_report.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Processing complete! Success rate: {report['success_rate']:.2%}")
    logger.info(f"Report saved to: {report_file}")
    
    return report

def main():
    parser = argparse.ArgumentParser(description="MediaPipe-based lip ROI extraction")
    parser.add_argument("--input_dir", required=True, help="Input directory with webm videos")
    parser.add_argument("--output_dir", required=True, help="Output directory for processed videos")
    parser.add_argument("--sample_size", type=int, help="Number of videos to process (for testing)")
    parser.add_argument("--padding", type=int, default=18, help="Padding around mouth region (pixels)")
    parser.add_argument("--target_size", type=int, nargs=2, default=[112, 112], help="Target size for output crops")
    
    args = parser.parse_args()
    
    logger.info("Starting MediaPipe lip ROI extraction pipeline")
    logger.info(f"Input directory: {args.input_dir}")
    logger.info(f"Output directory: {args.output_dir}")
    logger.info(f"Sample size: {args.sample_size or 'All files'}")
    
    # Process dataset
    report = process_dataset(args.input_dir, args.output_dir, args.sample_size)
    
    print(f"\n🎯 Processing Summary:")
    print(f"   Total videos: {report['total_videos']}")
    print(f"   Successful: {report['successful_videos']}")
    print(f"   Failed: {report['failed_videos']}")
    print(f"   Success rate: {report['success_rate']:.2%}")
    
    if report['success_rate'] < 0.8:
        print(f"\n⚠️  WARNING: Success rate below 80%. Check processing errors.")
        for error in report['processing_errors'][:5]:  # Show first 5 errors
            print(f"   - {error['file']}: {error['errors']}")

if __name__ == "__main__":
    main()
