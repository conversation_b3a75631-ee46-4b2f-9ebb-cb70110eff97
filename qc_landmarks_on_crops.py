#!/usr/bin/env python3
"""
Landmark Sanity Check on MediaPipe Crops
========================================

Runs MediaPipe on already-cropped ROIs to verify mouth is present and centered.
Outputs CSV report + short overlay videos showing detected mouth regions.

Usage:
    python qc_landmarks_on_crops.py --processed_dir data/preprocessed_mediapipe_full --check_per_class 20 --out qc/checks
"""

import os
import argparse
import random
import csv
import numpy as np
import cv2
import pandas as pd
import mediapipe as mp
from pathlib import Path

# Initialize MediaPipe Face Mesh
mp_face_mesh = mp.solutions.face_mesh

def load_processed_files(processed_dir):
    """Load all processed .npy files and extract metadata from filenames"""
    processed_path = Path(processed_dir)
    npy_files = list(processed_path.glob("*_processed_mediapipe.npy"))
    
    if not npy_files:
        raise SystemExit(f"No processed .npy files found in {processed_dir}")
    
    # Extract metadata from filenames
    data = []
    for npy_file in npy_files:
        # Parse filename: word__speaker__age__gender__ethnicity__timestamp_processed_mediapipe.npy
        filename = npy_file.stem.replace('_processed_mediapipe', '')
        parts = filename.split('__')
        
        if len(parts) >= 6:
            word = parts[0]
            speaker = parts[1]
            
            data.append({
                'video_path': str(npy_file),
                'phrase': word,
                'speaker': speaker,
                'filename': filename
            })
    
    return pd.DataFrame(data)

def mouth_bbox_from_mesh(face_landmarks, width, height):
    """Extract mouth bounding box from MediaPipe face mesh landmarks"""
    # Mouth landmark indices (outer and inner lip contours)
    mouth_indices = [
        61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,  # Outer lip
        78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308,         # Inner lip
        13, 82, 81, 80, 415, 310, 311, 312                        # Extended mouth
    ]
    
    # Remove duplicates and ensure indices are valid
    mouth_indices = [idx for idx in set(mouth_indices) if idx < len(face_landmarks.landmark)]
    
    if len(mouth_indices) < 10:  # Need minimum landmarks
        return None
    
    # Extract coordinates
    xs = [int(face_landmarks.landmark[i].x * width) for i in mouth_indices]
    ys = [int(face_landmarks.landmark[i].y * height) for i in mouth_indices]
    
    # Calculate bounding box
    x_min = max(min(xs), 0)
    y_min = max(min(ys), 0)
    x_max = min(max(xs), width - 1)
    y_max = min(max(ys), height - 1)
    
    return x_min, y_min, x_max, y_max

def score_video_clip(npy_path, sample_frames=12, show_video_path=None):
    """
    Score a video clip for mouth detection quality
    
    Returns:
        frac_good: Fraction of frames with good mouth detection
        mean_offsets: (mean_abs_dx, mean_abs_dy) from center
    """
    try:
        arr = np.load(npy_path)  # Shape: [T, 112, 112, 3]
    except Exception as e:
        print(f"[ERROR] Could not load {npy_path}: {e}")
        return 0.0, (None, None)
    
    if len(arr) == 0:
        return 0.0, (None, None)
    
    T, height, width, channels = arr.shape
    
    # Sample frames evenly across the video
    frame_indices = np.linspace(0, T-1, num=min(sample_frames, T), dtype=int)
    
    good_frames = 0
    center_x, center_y = width / 2, height / 2
    x_offsets, y_offsets = [], []
    
    # Setup video writer if requested
    video_writer = None
    if show_video_path:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(show_video_path, fourcc, 15.0, (width, height))
    
    # Initialize MediaPipe Face Mesh
    with mp_face_mesh.FaceMesh(
        static_image_mode=True,
        max_num_faces=1,
        refine_landmarks=True,
        min_detection_confidence=0.4,
        min_tracking_confidence=0.3
    ) as face_mesh:
        
        for frame_idx in frame_indices:
            frame = arr[frame_idx].copy()
            
            # Ensure frame is uint8
            if frame.dtype != np.uint8:
                frame = (frame * 255).astype(np.uint8)
            
            # Convert BGR to RGB for MediaPipe
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Process frame
            results = face_mesh.process(rgb_frame)
            
            mouth_detected = False
            if results.multi_face_landmarks:
                face_landmarks = results.multi_face_landmarks[0]
                mouth_bbox = mouth_bbox_from_mesh(face_landmarks, width, height)
                
                if mouth_bbox:
                    x1, y1, x2, y2 = mouth_bbox
                    
                    # Quality checks
                    margin = 6
                    mouth_width = x2 - x1
                    mouth_height = y2 - y1
                    
                    # Check if mouth is well-positioned and sized
                    inside_margins = (x1 > margin and y1 > margin and 
                                    x2 < width - margin and y2 < height - margin)
                    good_size = mouth_width > 10 and mouth_height > 8
                    
                    if inside_margins and good_size:
                        mouth_detected = True
                        good_frames += 1
                        
                        # Calculate offset from center
                        mouth_center_x = (x1 + x2) / 2
                        mouth_center_y = (y1 + y2) / 2
                        x_offsets.append(mouth_center_x - center_x)
                        y_offsets.append(mouth_center_y - center_y)
                        
                        # Draw green box for good detection
                        if video_writer:
                            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                            cv2.putText(frame, "GOOD", (x1, y1-5), 
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
                    else:
                        # Draw red box for poor detection
                        if video_writer:
                            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
                            cv2.putText(frame, "POOR", (x1, y1-5), 
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
            
            if not mouth_detected and video_writer:
                cv2.putText(frame, "NO MOUTH", (4, 20), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
            
            # Add center crosshair
            if video_writer:
                cv2.drawMarker(frame, (int(center_x), int(center_y)), (255, 255, 0),
                             markerType=cv2.MARKER_CROSS, markerSize=8, thickness=1)
                video_writer.write(frame)
    
    if video_writer:
        video_writer.release()
    
    # Calculate statistics
    fraction_good = good_frames / len(frame_indices) if len(frame_indices) > 0 else 0.0
    mean_abs_dx = float(np.mean(np.abs(x_offsets))) if len(x_offsets) > 0 else None
    mean_abs_dy = float(np.mean(np.abs(y_offsets))) if len(y_offsets) > 0 else None
    
    return fraction_good, (mean_abs_dx, mean_abs_dy)

def main():
    parser = argparse.ArgumentParser(description="Landmark sanity check on MediaPipe crops")
    parser.add_argument("--processed_dir", required=True,
                       help="Directory containing processed .npy files")
    parser.add_argument("--check_per_class", type=int, default=20,
                       help="Number of videos to check per class")
    parser.add_argument("--out", default="qc/checks",
                       help="Output directory for reports and videos")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.out, exist_ok=True)
    
    # Load processed files
    try:
        df = load_processed_files(args.processed_dir)
        print(f"Loaded {len(df)} processed videos")
    except Exception as e:
        print(f"Error loading processed files: {e}")
        return
    
    classes = sorted(df['phrase'].unique())
    print(f"Classes found: {classes}")
    
    # Process each class
    all_results = []
    class_summaries = []
    
    for class_name in classes:
        print(f"\n🔍 Processing class: {class_name}")
        
        class_videos = df[df['phrase'] == class_name]['video_path'].tolist()
        sample_videos = random.sample(class_videos, min(args.check_per_class, len(class_videos)))
        
        class_good_fractions = []
        class_dx_offsets = []
        class_dy_offsets = []
        
        for i, video_path in enumerate(sample_videos):
            # Create output video path
            video_filename = f"{class_name}_{i:02d}.mp4"
            video_output_path = os.path.join(args.out, video_filename)
            
            # Score the video
            frac_good, (mean_dx, mean_dy) = score_video_clip(
                video_path, 
                sample_frames=12,
                show_video_path=video_output_path
            )
            
            # Store results
            all_results.append([
                class_name,
                video_path,
                frac_good,
                mean_dx,
                mean_dy,
                video_output_path
            ])
            
            # Collect class statistics
            class_good_fractions.append(frac_good)
            if mean_dx is not None:
                class_dx_offsets.append(abs(mean_dx))
            if mean_dy is not None:
                class_dy_offsets.append(abs(mean_dy))
            
            dx_str = f"{mean_dx:.1f}" if mean_dx is not None else "N/A"
            dy_str = f"{mean_dy:.1f}" if mean_dy is not None else "N/A"
            print(f"  {os.path.basename(video_path)}: good_frames={frac_good:.2f}, "
                  f"dx={dx_str}, dy={dy_str}")
        
        # Calculate class summary
        avg_good_fraction = np.mean(class_good_fractions) if class_good_fractions else 0.0
        avg_dx_offset = np.mean(class_dx_offsets) if class_dx_offsets else None
        avg_dy_offset = np.mean(class_dy_offsets) if class_dy_offsets else None
        
        class_summaries.append([
            class_name,
            len(sample_videos),
            avg_good_fraction,
            avg_dx_offset,
            avg_dy_offset
        ])
        
        dx_avg_str = f"{avg_dx_offset:.1f}" if avg_dx_offset is not None else "N/A"
        dy_avg_str = f"{avg_dy_offset:.1f}" if avg_dy_offset is not None else "N/A"
        print(f"  📊 Class summary: avg_good={avg_good_fraction:.2f}, "
              f"avg_dx={dx_avg_str}, avg_dy={dy_avg_str}")
    
    # Save detailed results
    csv_path = os.path.join(args.out, "roi_qc_report.csv")
    with open(csv_path, "w", newline="") as f:
        writer = csv.writer(f)
        writer.writerow(["class", "npy_path", "frac_good_frames", "mean_abs_dx", "mean_abs_dy", "video_preview"])
        writer.writerows(all_results)
    
    # Save class summary
    summary_path = os.path.join(args.out, "class_summary.csv")
    with open(summary_path, "w", newline="") as f:
        writer = csv.writer(f)
        writer.writerow(["class", "videos_checked", "avg_good_fraction", "avg_abs_dx", "avg_abs_dy"])
        writer.writerows(class_summaries)
    
    # Print final summary
    print(f"\n🎯 QC Landmark Check Summary:")
    print(f"   Detailed report: {csv_path}")
    print(f"   Class summary: {summary_path}")
    print(f"   Video previews: {args.out}/*.mp4")
    
    print(f"\n📋 Quality Targets:")
    print(f"   ✅ frac_good_frames ≥ 0.9 (90% of frames have good mouth detection)")
    print(f"   ✅ mean_abs_dx/dy ≤ 8 px (mouth center near ROI center)")
    
    # Overall assessment
    overall_good_fraction = np.mean([row[2] for row in all_results])
    good_videos = sum(1 for row in all_results if row[2] >= 0.9)
    total_videos = len(all_results)
    
    print(f"\n🎯 Overall Assessment:")
    print(f"   Average good fraction: {overall_good_fraction:.2f}")
    print(f"   Videos meeting target (≥0.9): {good_videos}/{total_videos} ({good_videos/total_videos:.1%})")
    
    if overall_good_fraction >= 0.9 and good_videos/total_videos >= 0.8:
        print(f"   ✅ QUALITY CHECK PASSED - Ready for training!")
    else:
        print(f"   ❌ QUALITY CHECK FAILED - Consider reprocessing or adjusting parameters")
        print(f"   💡 Check the MP4 previews to see detection issues")

if __name__ == "__main__":
    main()
