#!/usr/bin/env python3
"""
Visual Comparison: Original vs Processed
=======================================

Create side-by-side comparison of original video frames vs processed crops
to understand what's going wrong with the preprocessing.
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def create_comparison(original_video_path: str, processed_npy_path: str, output_path: str):
    """Create side-by-side comparison of original vs processed"""
    
    print(f"🔍 Comparing:")
    print(f"   Original: {Path(original_video_path).name}")
    print(f"   Processed: {Path(processed_npy_path).name}")
    
    # Load original video
    cap = cv2.VideoCapture(original_video_path, cv2.CAP_FFMPEG)
    if not cap.isOpened():
        cap = cv2.VideoCapture(original_video_path)
        if not cap.isOpened():
            print("❌ Cannot open original video")
            return False
    
    # Read first few frames from original
    original_frames = []
    for i in range(5):  # First 5 frames
        ret, frame = cap.read()
        if ret:
            original_frames.append(frame)
        else:
            break
    cap.release()
    
    if not original_frames:
        print("❌ No frames from original video")
        return False
    
    # Load processed video
    try:
        processed_array = np.load(processed_npy_path)
        print(f"   Processed shape: {processed_array.shape}")
        
        if len(processed_array) == 0:
            print("❌ Empty processed array")
            return False
        
        # Take first 5 frames from processed
        processed_frames = processed_array[:5]
        
    except Exception as e:
        print(f"❌ Error loading processed file: {e}")
        return False
    
    # Create comparison figure
    n_frames = min(len(original_frames), len(processed_frames))
    fig, axes = plt.subplots(2, n_frames, figsize=(n_frames * 4, 8))
    
    if n_frames == 1:
        axes = axes.reshape(2, 1)
    
    fig.suptitle(f'Original vs Processed: {Path(original_video_path).stem}', fontsize=16)
    
    for i in range(n_frames):
        # Original frame (top row)
        original = original_frames[i]
        original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
        
        axes[0, i].imshow(original_rgb)
        axes[0, i].set_title(f'Original Frame {i}\n{original.shape[1]}x{original.shape[0]}')
        axes[0, i].axis('off')
        
        # Add center crosshair to original
        h, w = original.shape[:2]
        axes[0, i].axhline(y=h//2, color='yellow', linestyle='--', alpha=0.7)
        axes[0, i].axvline(x=w//2, color='yellow', linestyle='--', alpha=0.7)
        
        # Processed frame (bottom row)
        processed = processed_frames[i]
        
        # Ensure processed frame is uint8
        if processed.dtype != np.uint8:
            if processed.max() <= 1.0:
                processed = (processed * 255).astype(np.uint8)
            else:
                processed = processed.astype(np.uint8)
        
        processed_rgb = cv2.cvtColor(processed, cv2.COLOR_BGR2RGB)
        
        axes[1, i].imshow(processed_rgb)
        axes[1, i].set_title(f'Processed Frame {i}\n{processed.shape[1]}x{processed.shape[0]}')
        axes[1, i].axis('off')
        
        # Add center crosshair to processed
        h_p, w_p = processed.shape[:2]
        axes[1, i].axhline(y=h_p//2, color='red', linestyle='-', alpha=0.8)
        axes[1, i].axvline(x=w_p//2, color='red', linestyle='-', alpha=0.8)
        
        # Analyze processed frame
        gray = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
        mean_intensity = np.mean(gray)
        std_intensity = np.std(gray)
        
        # Add intensity info
        axes[1, i].text(5, 15, f'Mean: {mean_intensity:.1f}', 
                       color='white', fontsize=8, 
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='black', alpha=0.7))
        axes[1, i].text(5, 30, f'Std: {std_intensity:.1f}', 
                       color='white', fontsize=8,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='black', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Comparison saved: {output_path}")
    return True

def main():
    # Test cases - original videos and their processed counterparts
    test_cases = [
        {
            'original': 'data/top 5 dataset 30.8.25/doctor__useruser01__18to39__male__not_specified__20250827T052630.webm',
            'processed': 'data/processed_v2_fixed/doctor__useruser01__18to39__male__not_specified__20250827T052630_processed_v2.npy'
        },
        {
            'original': 'data/top 5 dataset 30.8.25/glasses__useruser01__65plus__female__caucasian__20250827T055622.webm',
            'processed': 'data/processed_v2_fixed/glasses__useruser01__65plus__female__caucasian__20250827T055622_processed_v2.npy'
        },
        {
            'original': 'data/top 5 dataset 30.8.25/pillow__useruser01__18to39__male__not_specified__20250820T091743.webm',
            'processed': 'data/processed_v2_fixed/pillow__useruser01__18to39__male__not_specified__20250820T091743_processed_v2.npy'
        }
    ]
    
    print("🚀 Visual Comparison: Original vs Processed")
    print("=" * 60)
    
    # Create output directory
    output_dir = Path("visual_comparisons")
    output_dir.mkdir(exist_ok=True)
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases):
        original_path = test_case['original']
        processed_path = test_case['processed']
        
        if not Path(original_path).exists():
            print(f"❌ Original video not found: {original_path}")
            continue
            
        if not Path(processed_path).exists():
            print(f"❌ Processed video not found: {processed_path}")
            continue
        
        output_path = output_dir / f"comparison_{i+1}_{Path(original_path).stem}.png"
        
        if create_comparison(original_path, processed_path, str(output_path)):
            success_count += 1
    
    print(f"\n🎯 Comparison Summary:")
    print(f"   Successful comparisons: {success_count}/{len(test_cases)}")
    print(f"   Output directory: {output_dir}")
    
    print(f"\n💡 What to look for in the comparisons:")
    print(f"   ✅ Are lips visible in the original frames?")
    print(f"   ✅ Are lips properly extracted in the processed crops?")
    print(f"   ❌ Are processed crops showing wrong regions (chin, nose, etc.)?")
    print(f"   ❌ Are processed crops too dark/bright/blurry?")
    print(f"   ❌ Are processed crops completely black/empty?")
    
    if success_count > 0:
        print(f"\n📋 Next Steps:")
        print(f"   1. Open the comparison images in {output_dir}")
        print(f"   2. Identify what's wrong with the preprocessing")
        print(f"   3. Adjust MediaPipe parameters or approach accordingly")

if __name__ == "__main__":
    main()
