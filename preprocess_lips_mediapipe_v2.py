#!/usr/bin/env python3
"""
MediaPipe Lip ROI Extraction Pipeline V2 - Robust Version
========================================================

Improved preprocessing with:
- Face Detection → Face Mesh pipeline for better reliability
- Adaptive contrast enhancement (CLAHE + gamma correction)
- Temporal smoothing of mouth center positions
- Tracker fallback for missed detections
- Proper mouth landmark-based cropping with centering

Usage:
    python preprocess_lips_mediapipe_v2.py --input_dir "data/top 5 dataset 30.8.25" --output_dir "data/processed_v2" --sample_size 50
"""

import os
import cv2
import numpy as np
import mediapipe as mp
import argparse
import json
from pathlib import Path
from typing import Tuple, Optional, List, Dict, Any
import logging
from tqdm import tqdm
from collections import deque

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# MediaPipe lip landmark indices
LIPS_OUTER = [61, 146, 91, 181, 84, 17, 314, 405, 321, 375, 291]
LIPS_INNER = [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308]
LIPS_ALL = LIPS_OUTER + LIPS_INNER

class RobustLipExtractor:
    """Robust MediaPipe-based lip extractor with fallback mechanisms"""
    
    def __init__(self, target_size: Tuple[int, int] = (112, 112)):
        """Initialize the robust lip extractor"""
        self.target_size = target_size
        
        # Initialize MediaPipe components
        self.mp_face_detection = mp.solutions.face_detection
        self.mp_face_mesh = mp.solutions.face_mesh
        
        logger.info(f"Robust Lip Extractor initialized - Target size: {target_size}")
    
    def clahe_gamma_enhancement(self, img: np.ndarray, std_thresh: float = 18, gamma: float = 1.3) -> np.ndarray:
        """Apply adaptive contrast enhancement for low-contrast frames"""
        if img.std() >= std_thresh:
            return img
        
        # Convert to YCrCb and apply CLAHE to Y channel
        ycrcb = cv2.cvtColor(img, cv2.COLOR_BGR2YCrCb)
        y, cr, cb = cv2.split(ycrcb)
        
        # Apply CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        y = clahe.apply(y)
        
        # Merge back
        ycrcb = cv2.merge([y, cr, cb])
        enhanced = cv2.cvtColor(ycrcb, cv2.COLOR_YCrCb2BGR)
        
        # Apply gamma correction
        inv_gamma = 1.0 / gamma
        table = (np.linspace(0, 1, 256) ** inv_gamma * 255).astype(np.uint8)
        enhanced = cv2.LUT(enhanced, table)
        
        return enhanced
    
    def mouth_bbox_from_landmarks(self, landmarks, width: int, height: int,
                                 scale: float = 1.8, min_side: int = 50) -> Tuple[int, int, int, int, Tuple[int, int]]:
        """Extract mouth bounding box from MediaPipe landmarks"""
        
        # Extract mouth landmark coordinates
        xs, ys = [], []
        for i in LIPS_ALL:
            if i < len(landmarks.landmark):
                point = landmarks.landmark[i]
                xs.append(int(point.x * width))
                ys.append(int(point.y * height))
        
        if len(xs) < 10:  # Need sufficient landmarks
            return None
        
        # Calculate bounding box (handle potential negative coordinates)
        x1, x2 = min(xs), max(xs)
        y1, y2 = min(ys), max(ys)

        # Calculate center (use raw coordinates first)
        cx, cy = (x1 + x2) // 2, (y1 + y2) // 2

        # Calculate expanded size
        mouth_width = x2 - x1
        mouth_height = y2 - y1
        side = int(max(mouth_width, mouth_height) * scale)
        side = max(side, min_side)

        # Create square bounding box centered on mouth
        x1_new = cx - side // 2
        y1_new = cy - side // 2
        x2_new = x1_new + side
        y2_new = y1_new + side

        # Smart clamping - preserve mouth center when possible
        # If mouth extends beyond frame, shift the entire crop region
        if x1_new < 0:
            shift = -x1_new
            x1_new = 0
            x2_new = min(width, side)
        elif x2_new > width:
            shift = x2_new - width
            x2_new = width
            x1_new = max(0, width - side)

        if y1_new < 0:
            shift = -y1_new
            y1_new = 0
            y2_new = min(height, side)
        elif y2_new > height:
            shift = y2_new - height
            y2_new = height
            y1_new = max(0, height - side)

        # Final safety clamp
        x1_new = max(0, min(x1_new, width - 1))
        y1_new = max(0, min(y1_new, height - 1))
        x2_new = max(x1_new + 1, min(x2_new, width))
        y2_new = max(y1_new + 1, min(y2_new, height))
        
        return x1_new, y1_new, x2_new, y2_new, (cx, cy)
    
    def process_video_frames(self, frames_bgr: List[np.ndarray], debug: bool = False) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Process video frames with robust mouth detection and tracking"""
        
        if not frames_bgr:
            return None, {'error': 'No frames provided'}
        
        h, w = frames_bgr[0].shape[:2]
        processed_frames = []
        centers_history = deque(maxlen=5)  # Temporal smoothing
        tracker = None
        
        stats = {
            'total_frames': len(frames_bgr),
            'face_mesh_detections': 0,
            'tracker_updates': 0,
            'failed_frames': 0,
            'enhanced_frames': 0
        }
        
        # Initialize MediaPipe components
        with self.mp_face_detection.FaceDetection(
            model_selection=1, 
            min_detection_confidence=0.3
        ) as face_detection, \
        self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.3,
            min_tracking_confidence=0.3
        ) as face_mesh:
            
            for frame_idx, frame in enumerate(frames_bgr):
                # Apply contrast enhancement if needed
                original_std = frame.std()
                if original_std < 18:
                    frame = self.clahe_gamma_enhancement(frame)
                    stats['enhanced_frames'] += 1
                
                # Convert to RGB for MediaPipe
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                mouth_box = None
                
                # Try Face Mesh detection first
                face_mesh_results = face_mesh.process(rgb_frame)
                
                if face_mesh_results.multi_face_landmarks:
                    # Extract mouth bounding box from landmarks
                    landmarks = face_mesh_results.multi_face_landmarks[0]
                    bbox_result = self.mouth_bbox_from_landmarks(landmarks, w, h)
                    
                    if bbox_result:
                        x1, y1, x2, y2, (cx, cy) = bbox_result
                        mouth_box = (x1, y1, x2, y2)
                        centers_history.append((cx, cy))
                        stats['face_mesh_detections'] += 1
                        
                        # Reset tracker on successful detection
                        tracker = None
                
                # Fallback to tracker if Face Mesh failed
                if mouth_box is None and len(centers_history) > 0:
                    # Initialize tracker if needed
                    if tracker is None:
                        # Create initial tracking box around last known center
                        cx, cy = centers_history[-1]
                        side = 80
                        track_x = max(0, cx - side // 2)
                        track_y = max(0, cy - side // 2)
                        track_box = (track_x, track_y, side, side)
                        
                        try:
                            tracker = cv2.legacy.TrackerCSRT_create()
                            tracker.init(frame, track_box)
                        except:
                            tracker = None
                    
                    # Update tracker
                    if tracker is not None:
                        success, bbox = tracker.update(frame)
                        if success:
                            x, y, w_track, h_track = map(int, bbox)
                            mouth_box = (x, y, x + w_track, y + h_track)
                            stats['tracker_updates'] += 1
                        else:
                            # Tracker lost, reset
                            tracker = None
                
                # Extract and process crop
                if mouth_box is not None:
                    x1, y1, x2, y2 = mouth_box
                    
                    # Ensure valid crop region
                    if x2 > x1 and y2 > y1:
                        crop = frame[y1:y2, x1:x2]
                        
                        # Resize to target size
                        if crop.size > 0:
                            crop_resized = cv2.resize(crop, self.target_size, interpolation=cv2.INTER_AREA)
                            
                            # Add debug markers if requested
                            if debug:
                                center_x, center_y = self.target_size[0] // 2, self.target_size[1] // 2
                                cv2.drawMarker(crop_resized, (center_x, center_y), (0, 255, 0),
                                             markerType=cv2.MARKER_CROSS, markerSize=10, thickness=2)
                            
                            processed_frames.append(crop_resized)
                        else:
                            # Empty crop, create blank frame
                            blank_frame = np.zeros((*self.target_size, 3), dtype=np.uint8)
                            processed_frames.append(blank_frame)
                            stats['failed_frames'] += 1
                    else:
                        # Invalid bounding box
                        blank_frame = np.zeros((*self.target_size, 3), dtype=np.uint8)
                        processed_frames.append(blank_frame)
                        stats['failed_frames'] += 1
                else:
                    # No detection at all, create blank frame
                    blank_frame = np.zeros((*self.target_size, 3), dtype=np.uint8)
                    processed_frames.append(blank_frame)
                    stats['failed_frames'] += 1
        
        if not processed_frames:
            return None, {'error': 'No frames could be processed'}
        
        # Convert to numpy array
        video_array = np.array(processed_frames)  # Shape: [T, H, W, C]
        
        # Calculate success rate
        stats['success_rate'] = (stats['total_frames'] - stats['failed_frames']) / stats['total_frames']
        stats['output_shape'] = video_array.shape
        
        return video_array, stats
    
    def process_video_file(self, video_path: str, max_frames: int = 32) -> Tuple[Optional[np.ndarray], Dict[str, Any]]:
        """Process a single video file"""
        
        stats = {
            'success': False,
            'video_path': video_path,
            'total_frames': 0,
            'processed_frames': 0,
            'errors': []
        }
        
        try:
            # Open video
            cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
            if not cap.isOpened():
                cap = cv2.VideoCapture(video_path)
                if not cap.isOpened():
                    stats['errors'].append('Could not open video file')
                    return None, stats
            
            # Read all frames
            frames = []
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
                frame_count += 1
                
                # Safety limit
                if frame_count > 500:
                    break
            
            cap.release()
            
            if not frames:
                stats['errors'].append('No frames could be read from video')
                return None, stats
            
            stats['total_frames'] = len(frames)
            
            # Sample frames if we have more than needed
            if len(frames) > max_frames:
                # Evenly sample frames
                indices = np.linspace(0, len(frames) - 1, max_frames, dtype=int)
                frames = [frames[i] for i in indices]
            
            # Process frames
            video_array, processing_stats = self.process_video_frames(frames)
            
            if video_array is not None:
                # Pad or trim to exact frame count
                while len(video_array) < max_frames:
                    # Repeat last frame
                    video_array = np.concatenate([video_array, video_array[-1:]], axis=0)
                
                video_array = video_array[:max_frames]  # Trim if needed
                
                stats.update({
                    'success': True,
                    'processed_frames': len(video_array),
                    'processing_stats': processing_stats,
                    'output_shape': video_array.shape
                })
                
                return video_array, stats
            else:
                stats['errors'].append('Frame processing failed')
                return None, stats
                
        except Exception as e:
            stats['errors'].append(f'Video processing error: {str(e)}')
            logger.error(f"Error processing {video_path}: {e}")
            return None, stats

def process_dataset_v2(input_dir: str, output_dir: str, sample_size: Optional[int] = None) -> Dict[str, Any]:
    """Process dataset with robust MediaPipe v2 pipeline"""
    
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Find video files
    video_files = list(input_path.glob("*.webm"))
    
    if sample_size:
        video_files = video_files[:sample_size]
    
    logger.info(f"Found {len(video_files)} video files to process")
    
    # Initialize extractor
    extractor = RobustLipExtractor(target_size=(112, 112))
    
    # Processing report
    report = {
        'total_videos': len(video_files),
        'successful_videos': 0,
        'failed_videos': 0,
        'processing_errors': [],
        'success_rate': 0.0,
        'output_directory': str(output_path),
        'processed_files': []
    }
    
    # Process videos
    for video_file in tqdm(video_files, desc="Processing videos"):
        try:
            video_array, stats = extractor.process_video_file(str(video_file), max_frames=32)
            
            if video_array is not None and stats['success']:
                # Save processed video
                output_file = output_path / f"{video_file.stem}_processed_v2.npy"
                np.save(output_file, video_array)
                
                # Save metadata
                metadata_file = output_path / f"{video_file.stem}_metadata_v2.json"
                with open(metadata_file, 'w') as f:
                    json.dump(stats, f, indent=2)
                
                report['successful_videos'] += 1
                report['processed_files'].append({
                    'input_file': str(video_file),
                    'output_file': str(output_file),
                    'metadata_file': str(metadata_file),
                    'stats': stats
                })
                
                logger.info(f"✅ Processed: {video_file.name}")
            else:
                report['failed_videos'] += 1
                report['processing_errors'].append({
                    'file': str(video_file),
                    'errors': stats.get('errors', ['Unknown error'])
                })
                logger.warning(f"❌ Failed: {video_file.name}")
                
        except Exception as e:
            report['failed_videos'] += 1
            error_msg = f"Exception processing {video_file}: {str(e)}"
            report['processing_errors'].append({
                'file': str(video_file),
                'errors': [error_msg]
            })
            logger.error(error_msg)
    
    # Calculate final statistics
    report['success_rate'] = report['successful_videos'] / report['total_videos'] if report['total_videos'] > 0 else 0.0
    
    # Save report
    report_file = output_path / "processing_report_v2.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Processing complete! Success rate: {report['success_rate']:.2%}")
    logger.info(f"Report saved to: {report_file}")
    
    return report

def main():
    parser = argparse.ArgumentParser(description="Robust MediaPipe v2 lip ROI extraction")
    parser.add_argument("--input_dir", required=True, help="Input directory with webm videos")
    parser.add_argument("--output_dir", required=True, help="Output directory for processed videos")
    parser.add_argument("--sample_size", type=int, help="Number of videos to process (for testing)")
    
    args = parser.parse_args()
    
    logger.info("Starting MediaPipe v2 robust lip ROI extraction pipeline")
    logger.info(f"Input directory: {args.input_dir}")
    logger.info(f"Output directory: {args.output_dir}")
    logger.info(f"Sample size: {args.sample_size or 'All files'}")
    
    # Process dataset
    report = process_dataset_v2(args.input_dir, args.output_dir, args.sample_size)
    
    print(f"\n🎯 Processing Summary:")
    print(f"   Total videos: {report['total_videos']}")
    print(f"   Successful: {report['successful_videos']}")
    print(f"   Failed: {report['failed_videos']}")
    print(f"   Success rate: {report['success_rate']:.2%}")
    
    if report['success_rate'] >= 0.9:
        print(f"\n✅ EXCELLENT SUCCESS RATE - Ready for QC validation")
    elif report['success_rate'] >= 0.8:
        print(f"\n⚠️  GOOD SUCCESS RATE - Proceed with QC validation")
    else:
        print(f"\n❌ LOW SUCCESS RATE - Review processing errors")

if __name__ == "__main__":
    main()
